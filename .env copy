# Journal App Environment Variables

# Server Configuration
PORT=8080

# Database Configuration
DB_FILE=/var/lib/data/journal.db

# Audio Files Directory
AUDIO_DIR=/var/lib/data/audiofiles

# Gemini API Configuration
GEMINI_API_KEY=
GEMINI_MODEL=gemini-2.0-flash

# Prompt Templates
SUMMARY_PROMPT=Summarize the following journal entry concisely in one or two sentences: 
TRANSCRIPTION_PROMPT=Transcribe the following audio recording:

# Upload Limits
MAX_UPLOAD_SIZE_MB=20

# Chat Configuration
MAX_ENTRIES_FOR_CHAT_CONTEXT=5
MAX_HISTORY_TURNS_FOR_CONTEXT=20
MAX_HISTORY_MESSAGES_TO_FETCH=50
