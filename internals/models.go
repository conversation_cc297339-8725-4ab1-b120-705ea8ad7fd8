package internals

import "time"

type User struct {
	ID        int
	Email     string
	NickName  string
	FirstName string
	LastName  string
	Age       int
	Gender    string
	Avatar    string
	Timezone  string
	CreatedAt time.Time
}

var store *DatabaseSessionStore

type contextKey string

const UserSessionKey contextKey = "userSession"

// PageData represents the data structure we'll pass to our templates
type PageData struct {
	IsLoggedIn bool
	UserName   string
	Avatar     string
}

// GoogleConfig holds the configuration for Google OAuth
type GoogleConfig struct {
	ClientID     string
	ClientSecret string
	RedirectURI  string
}

// GoogleUserInfo represents the user information received from Google
type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	Username      string `json:"username"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Gender        string `json:"gender"`
}

// GoogleTokenResponse represents the OAuth token response
type GoogleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
}

// Create a map to store state tokens to prevent CSRF attacks
var stateTokens = make(map[string]time.Time)

// Initialize Google configuration
var googleConfig = &GoogleConfig{
	ClientID:     "163539294903-lk9m0haurb6q966tmldgsboul6hd7ne9.apps.googleusercontent.com",
	ClientSecret: "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",
	RedirectURI:  "https://journalmine-3sctf.kinsta.app//auth/google/callback",
}

type DBConfig struct {
	Type     string
	Host     string
	Port     string
	User     string
	Password string
	Name     string
	SSLMode  string
}
